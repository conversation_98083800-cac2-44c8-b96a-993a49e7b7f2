/*
  Wio Terminal Blink
  
  This example code demonstrates basic LED blinking functionality on the Wio Terminal.
  The built-in LED will turn on for one second, then off for one second, repeatedly.
  
  The Wio Terminal has a built-in blue LED that can be controlled using the LED_BUILTIN constant.
  
  Hardware:
  - Seeed Studio Wio Terminal
  
  Created: 2025-06-27
  Author: Augment Agent
*/

void setup() {
  // Initialize serial communication for debugging (optional)
  Serial.begin(115200);
  
  // Initialize the built-in LED pin as an output
  pinMode(LED_BUILTIN, OUTPUT);
  
  // Print startup message
  Serial.println("Wio Terminal Blink Program Started");
  Serial.println("Built-in LED will blink every second");
}

void loop() {
  // Turn the LED on (HIGH is the voltage level)
  digitalWrite(LED_BUILTIN, HIGH);
  Serial.println("LED ON");
  
  // Wait for a second
  delay(1000);
  
  // Turn the LED off by making the voltage LOW
  digitalWrite(LED_BUILTIN, LOW);
  Serial.println("LED OFF");
  
  // Wait for a second
  delay(1000);
}
